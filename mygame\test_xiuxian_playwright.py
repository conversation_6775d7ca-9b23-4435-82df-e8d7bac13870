#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修仙MUD Playwright自动化测试
测试所有Handler功能的Web界面
"""

import asyncio
import json
from playwright.async_api import async_playwright
import time

class XiuxianPlaywrightTest:
    def __init__(self):
        self.base_url = "http://localhost:4005"
        self.test_results = []
        
    async def run_tests(self):
        """运行所有测试"""
        async with async_playwright() as p:
            # 启动浏览器
            browser = await p.chromium.launch(headless=False)  # 设置为False以便观察测试过程
            context = await browser.new_context()
            page = await context.new_page()
            
            try:
                print("🚀 开始修仙MUD Playwright测试...")
                
                # 导航到测试页面
                await self.navigate_to_test_page(page)
                
                # 等待页面加载
                await page.wait_for_timeout(2000)
                
                # 测试修仙系统
                await self.test_cultivation_system(page)
                
                # 测试战斗技能系统
                await self.test_combat_system(page)
                
                # 测试炼丹系统
                await self.test_alchemy_system(page)
                
                # 测试因果系统
                await self.test_karma_system(page)
                
                # 测试AI导演系统
                await self.test_ai_director_system(page)
                
                # 生成测试报告
                self.generate_report()
                
            except Exception as e:
                print(f"❌ 测试过程中发生错误: {e}")
                self.test_results.append({
                    "test": "整体测试",
                    "status": "失败",
                    "error": str(e)
                })
            finally:
                await browser.close()
    
    async def navigate_to_test_page(self, page):
        """导航到测试页面"""
        try:
            print("📍 导航到修仙测试页面...")
            await page.goto(f"{self.base_url}/xiuxian-test/")
            
            # 等待页面标题加载
            await page.wait_for_selector("h1", timeout=10000)
            title = await page.text_content("h1")
            
            if "修仙MUD Handler测试界面" in title:
                print("✅ 成功加载测试页面")
                self.test_results.append({
                    "test": "页面加载",
                    "status": "成功",
                    "details": f"页面标题: {title}"
                })
            else:
                raise Exception(f"页面标题不正确: {title}")
                
        except Exception as e:
            print(f"❌ 页面加载失败: {e}")
            self.test_results.append({
                "test": "页面加载",
                "status": "失败",
                "error": str(e)
            })
            raise
    
    async def test_cultivation_system(self, page):
        """测试修仙系统"""
        print("\n🧘‍♂️ 测试修仙系统...")
        
        # 测试获取当前境界
        await self.click_and_verify(page, "getCultivationRealm", "cultivation-result", "获取当前境界")
        
        # 测试获取修炼进度
        await self.click_and_verify(page, "getCultivationProgress", "cultivation-result", "获取修炼进度")
        
        # 测试开始修炼
        await self.click_and_verify(page, "startCultivation", "cultivation-result", "开始修炼")
        
        # 测试尝试突破
        await self.click_and_verify(page, "attemptBreakthrough", "cultivation-result", "尝试突破")
    
    async def test_combat_system(self, page):
        """测试战斗技能系统"""
        print("\n⚔️ 测试战斗技能系统...")
        
        # 测试获取可用技能
        await self.click_and_verify(page, "getAvailableSkills", "combat-result", "获取可用技能")
        
        # 测试获取已学技能
        await self.click_and_verify(page, "getLearnedSkills", "combat-result", "获取已学技能")
        
        # 测试学习技能
        await page.fill("#skill-name", "火球术")
        await self.click_and_verify(page, "learnSkill", "combat-result", "学习技能")
    
    async def test_alchemy_system(self, page):
        """测试炼丹系统"""
        print("\n🧪 测试炼丹系统...")
        
        # 测试获取配方
        await self.click_and_verify(page, "getAlchemyRecipes", "alchemy-result", "获取配方")
        
        # 测试获取材料
        await self.click_and_verify(page, "getMaterials", "alchemy-result", "获取材料")
        
        # 测试添加材料
        await page.fill("#material-name", "灵石")
        await page.fill("#material-amount", "5")
        await self.click_and_verify(page, "addMaterial", "alchemy-result", "添加材料")
    
    async def test_karma_system(self, page):
        """测试因果系统"""
        print("\n⚖️ 测试因果系统...")
        
        # 测试获取因果状态
        await self.click_and_verify(page, "getKarmaStatus", "karma-result", "获取因果状态")
        
        # 测试记录善行
        await page.select_option("#karma-type", "good")
        await page.fill("#karma-amount", "10")
        await page.fill("#karma-desc", "帮助他人")
        await self.click_and_verify(page, "recordKarma", "karma-result", "记录善行")
        
        # 测试记录恶行
        await page.select_option("#karma-type", "evil")
        await page.fill("#karma-amount", "5")
        await page.fill("#karma-desc", "伤害无辜")
        await self.click_and_verify(page, "recordKarma", "karma-result", "记录恶行")
    
    async def test_ai_director_system(self, page):
        """测试AI导演系统"""
        print("\n🎭 测试AI导演系统...")
        
        # 测试获取故事状态
        await self.click_and_verify(page, "getStoryStatus", "ai-director-result", "获取故事状态")
        
        # 测试获取世界状态
        await self.click_and_verify(page, "getWorldState", "ai-director-result", "获取世界状态")
        
        # 测试更新上下文
        await page.fill("#context", "角色开始修炼")
        await self.click_and_verify(page, "updateContext", "ai-director-result", "更新上下文")
    
    async def click_and_verify(self, page, function_name, result_id, test_name):
        """点击按钮并验证结果"""
        try:
            # 点击按钮
            await page.evaluate(f"{function_name}()")
            
            # 等待结果更新
            await page.wait_for_timeout(2000)
            
            # 获取结果
            result_element = await page.query_selector(f"#{result_id}")
            if result_element:
                result_text = await result_element.text_content()
                
                # 检查是否包含JSON响应
                if result_text and ("success" in result_text or "data" in result_text):
                    print(f"  ✅ {test_name}: 成功")
                    self.test_results.append({
                        "test": test_name,
                        "status": "成功",
                        "response": result_text[:200] + "..." if len(result_text) > 200 else result_text
                    })
                else:
                    print(f"  ⚠️ {test_name}: 响应异常")
                    self.test_results.append({
                        "test": test_name,
                        "status": "警告",
                        "response": result_text[:200] + "..." if len(result_text) > 200 else result_text
                    })
            else:
                print(f"  ❌ {test_name}: 未找到结果元素")
                self.test_results.append({
                    "test": test_name,
                    "status": "失败",
                    "error": "未找到结果元素"
                })
                
        except Exception as e:
            print(f"  ❌ {test_name}: {e}")
            self.test_results.append({
                "test": test_name,
                "status": "失败",
                "error": str(e)
            })
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "="*60)
        print("📊 修仙MUD Playwright测试报告")
        print("="*60)
        
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r["status"] == "成功"])
        failed_tests = len([r for r in self.test_results if r["status"] == "失败"])
        warning_tests = len([r for r in self.test_results if r["status"] == "警告"])
        
        print(f"总测试数: {total_tests}")
        print(f"成功: {successful_tests}")
        print(f"失败: {failed_tests}")
        print(f"警告: {warning_tests}")
        print(f"成功率: {(successful_tests/total_tests*100):.1f}%")
        
        print("\n详细结果:")
        for result in self.test_results:
            status_icon = "✅" if result["status"] == "成功" else "❌" if result["status"] == "失败" else "⚠️"
            print(f"{status_icon} {result['test']}: {result['status']}")
            if "error" in result:
                print(f"   错误: {result['error']}")
            elif "response" in result:
                print(f"   响应: {result['response'][:100]}...")
        
        # 保存详细报告到文件
        with open("xiuxian_playwright_test_report.json", "w", encoding="utf-8") as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: xiuxian_playwright_test_report.json")
        
        if failed_tests == 0:
            print("\n🎉 所有测试通过！修仙MUD Handler系统Web界面功能正常！")
        else:
            print(f"\n⚠️ 有 {failed_tests} 个测试失败，请检查相关功能。")

async def main():
    """主函数"""
    tester = XiuxianPlaywrightTest()
    await tester.run_tests()

if __name__ == "__main__":
    print("🧙‍♂️ 修仙MUD Playwright自动化测试启动...")
    print("确保Evennia服务器正在运行在 http://localhost:4005")
    print("测试将在3秒后开始...")
    time.sleep(3)
    
    asyncio.run(main())
