<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TagProperty高性能查询系统测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .test-section h3 {
            color: #4CAF50;
            margin-top: 0;
        }
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            background: white;
            border-left: 4px solid #4CAF50;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .error {
            border-left-color: #f44336;
            background: #ffebee;
        }
        .success {
            border-left-color: #4CAF50;
            background: #e8f5e8;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
        }
        .stat-label {
            color: #666;
            font-size: 12px;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .input-group input, .input-group select {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>TagProperty高性能查询系统测试</h1>
            <p>验证修仙MUD的TagProperty功能和性能</p>
        </div>

        <!-- 修仙系统测试 -->
        <div class="test-section">
            <h3>🧘 修仙系统测试</h3>
            <div class="input-group">
                <label>境界:</label>
                <select id="realm-select">
                    <option value="练气期">练气期</option>
                    <option value="筑基期">筑基期</option>
                    <option value="金丹期">金丹期</option>
                    <option value="元婴期">元婴期</option>
                    <option value="化神期">化神期</option>
                </select>
            </div>
            <div class="input-group">
                <label>层次:</label>
                <input type="number" id="level-input" min="1" max="12" value="1">
            </div>
            <button class="button" onclick="testCultivationRealm()">获取当前境界</button>
            <button class="button" onclick="testCultivationProgress()">获取修炼进度</button>
            <button class="button" onclick="testCultivationStart()">开始修炼</button>
            <button class="button" onclick="testCultivationBreakthrough()">尝试突破</button>
            <div id="cultivation-result" class="result" style="display:none;"></div>
        </div>

        <!-- 战斗技能测试 -->
        <div class="test-section">
            <h3>⚔️ 战斗技能测试</h3>
            <div class="input-group">
                <label>技能名称:</label>
                <input type="text" id="skill-name" value="基础剑法" placeholder="输入技能名称">
            </div>
            <button class="button" onclick="testCombatAvailableSkills()">获取可用技能</button>
            <button class="button" onclick="testCombatLearnedSkills()">获取已学技能</button>
            <button class="button" onclick="testCombatLearnSkill()">学习技能</button>
            <div id="combat-result" class="result" style="display:none;"></div>
        </div>

        <!-- 炼丹系统测试 -->
        <div class="test-section">
            <h3>🧪 炼丹系统测试</h3>
            <div class="input-group">
                <label>材料名称:</label>
                <input type="text" id="material-name" value="灵芝" placeholder="输入材料名称">
            </div>
            <div class="input-group">
                <label>数量:</label>
                <input type="number" id="material-amount" min="1" value="1">
            </div>
            <button class="button" onclick="testAlchemyRecipes()">获取配方</button>
            <button class="button" onclick="testAlchemyMaterials()">获取材料</button>
            <button class="button" onclick="testAlchemyAddMaterial()">添加材料</button>
            <div id="alchemy-result" class="result" style="display:none;"></div>
        </div>

        <!-- 因果系统测试 -->
        <div class="test-section">
            <h3>☯️ 因果系统测试</h3>
            <div class="input-group">
                <label>因果类型:</label>
                <select id="karma-type">
                    <option value="good">善业</option>
                    <option value="evil">恶业</option>
                </select>
            </div>
            <div class="input-group">
                <label>数量:</label>
                <input type="number" id="karma-amount" min="1" value="10">
            </div>
            <div class="input-group">
                <label>描述:</label>
                <input type="text" id="karma-description" value="测试因果记录" placeholder="输入描述">
            </div>
            <button class="button" onclick="testKarmaStatus()">获取因果状态</button>
            <button class="button" onclick="testKarmaRecord()">记录因果</button>
            <div id="karma-result" class="result" style="display:none;"></div>
        </div>

        <!-- AI导演系统测试 -->
        <div class="test-section">
            <h3>🎭 AI导演系统测试</h3>
            <div class="input-group">
                <label>上下文:</label>
                <input type="text" id="ai-context" value="角色正在修炼" placeholder="输入上下文">
            </div>
            <button class="button" onclick="testAIDirectorStoryStatus()">获取故事状态</button>
            <button class="button" onclick="testAIDirectorWorldState()">获取世界状态</button>
            <button class="button" onclick="testAIDirectorUpdateContext()">更新上下文</button>
            <div id="ai-result" class="result" style="display:none;"></div>
        </div>

        <!-- 性能统计 -->
        <div class="test-section">
            <h3>📊 性能统计</h3>
            <button class="button" onclick="runPerformanceTest()">运行性能测试</button>
            <button class="button" onclick="runAllTests()">运行所有测试</button>
            <div class="stats" id="performance-stats" style="display:none;">
                <div class="stat-card">
                    <div class="stat-value" id="total-requests">0</div>
                    <div class="stat-label">总请求数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="avg-response-time">0ms</div>
                    <div class="stat-label">平均响应时间</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="success-rate">0%</div>
                    <div class="stat-label">成功率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="total-time">0ms</div>
                    <div class="stat-label">总耗时</div>
                </div>
            </div>
            <div id="performance-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        // 性能统计
        let performanceStats = {
            totalRequests: 0,
            totalTime: 0,
            successCount: 0,
            responseTimes: []
        };

        // 通用API调用函数
        async function apiCall(url, method = 'GET', data = null) {
            const startTime = performance.now();
            performanceStats.totalRequests++;
            
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                const result = await response.json();
                
                const endTime = performance.now();
                const responseTime = endTime - startTime;
                performanceStats.responseTimes.push(responseTime);
                performanceStats.totalTime += responseTime;
                
                if (result.success) {
                    performanceStats.successCount++;
                }
                
                updatePerformanceStats();
                return result;
            } catch (error) {
                const endTime = performance.now();
                const responseTime = endTime - startTime;
                performanceStats.responseTimes.push(responseTime);
                performanceStats.totalTime += responseTime;
                
                updatePerformanceStats();
                throw error;
            }
        }

        // 更新性能统计显示
        function updatePerformanceStats() {
            document.getElementById('total-requests').textContent = performanceStats.totalRequests;
            
            const avgTime = performanceStats.responseTimes.length > 0 
                ? (performanceStats.totalTime / performanceStats.responseTimes.length).toFixed(2)
                : 0;
            document.getElementById('avg-response-time').textContent = avgTime + 'ms';
            
            const successRate = performanceStats.totalRequests > 0 
                ? ((performanceStats.successCount / performanceStats.totalRequests) * 100).toFixed(1)
                : 0;
            document.getElementById('success-rate').textContent = successRate + '%';
            
            document.getElementById('total-time').textContent = performanceStats.totalTime.toFixed(2) + 'ms';
            
            document.getElementById('performance-stats').style.display = 'grid';
        }

        // 显示结果
        function showResult(elementId, result, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result ' + (isError ? 'error' : 'success');
            element.textContent = typeof result === 'string' ? result : JSON.stringify(result, null, 2);
        }

        // 修仙系统测试函数
        async function testCultivationRealm() {
            try {
                const result = await apiCall('/api/xiuxian/cultivation/realm/');
                showResult('cultivation-result', result);
            } catch (error) {
                showResult('cultivation-result', '错误: ' + error.message, true);
            }
        }

        async function testCultivationProgress() {
            try {
                const result = await apiCall('/api/xiuxian/cultivation/progress/');
                showResult('cultivation-result', result);
            } catch (error) {
                showResult('cultivation-result', '错误: ' + error.message, true);
            }
        }

        async function testCultivationStart() {
            try {
                const result = await apiCall('/api/xiuxian/cultivation/cultivate/', 'POST');
                showResult('cultivation-result', result);
            } catch (error) {
                showResult('cultivation-result', '错误: ' + error.message, true);
            }
        }

        async function testCultivationBreakthrough() {
            try {
                const result = await apiCall('/api/xiuxian/cultivation/breakthrough/', 'POST');
                showResult('cultivation-result', result);
            } catch (error) {
                showResult('cultivation-result', '错误: ' + error.message, true);
            }
        }

        // 战斗技能测试函数
        async function testCombatAvailableSkills() {
            try {
                const result = await apiCall('/api/xiuxian/combat/available_skills/');
                showResult('combat-result', result);
            } catch (error) {
                showResult('combat-result', '错误: ' + error.message, true);
            }
        }

        async function testCombatLearnedSkills() {
            try {
                const result = await apiCall('/api/xiuxian/combat/learned_skills/');
                showResult('combat-result', result);
            } catch (error) {
                showResult('combat-result', '错误: ' + error.message, true);
            }
        }

        async function testCombatLearnSkill() {
            try {
                const skillName = document.getElementById('skill-name').value;
                const result = await apiCall('/api/xiuxian/combat/learn_skill/', 'POST', {
                    skill_name: skillName
                });
                showResult('combat-result', result);
            } catch (error) {
                showResult('combat-result', '错误: ' + error.message, true);
            }
        }

        // 炼丹系统测试函数
        async function testAlchemyRecipes() {
            try {
                const result = await apiCall('/api/xiuxian/alchemy/recipes/');
                showResult('alchemy-result', result);
            } catch (error) {
                showResult('alchemy-result', '错误: ' + error.message, true);
            }
        }

        async function testAlchemyMaterials() {
            try {
                const result = await apiCall('/api/xiuxian/alchemy/materials/');
                showResult('alchemy-result', result);
            } catch (error) {
                showResult('alchemy-result', '错误: ' + error.message, true);
            }
        }

        async function testAlchemyAddMaterial() {
            try {
                const name = document.getElementById('material-name').value;
                const amount = parseInt(document.getElementById('material-amount').value);
                const result = await apiCall('/api/xiuxian/alchemy/add_material/', 'POST', {
                    name: name,
                    amount: amount
                });
                showResult('alchemy-result', result);
            } catch (error) {
                showResult('alchemy-result', '错误: ' + error.message, true);
            }
        }

        // 因果系统测试函数
        async function testKarmaStatus() {
            try {
                const result = await apiCall('/api/xiuxian/karma/status/');
                showResult('karma-result', result);
            } catch (error) {
                showResult('karma-result', '错误: ' + error.message, true);
            }
        }

        async function testKarmaRecord() {
            try {
                const karmaType = document.getElementById('karma-type').value;
                const amount = parseInt(document.getElementById('karma-amount').value);
                const description = document.getElementById('karma-description').value;
                const result = await apiCall('/api/xiuxian/karma/record/', 'POST', {
                    karma_type: karmaType,
                    amount: amount,
                    description: description
                });
                showResult('karma-result', result);
            } catch (error) {
                showResult('karma-result', '错误: ' + error.message, true);
            }
        }

        // AI导演系统测试函数
        async function testAIDirectorStoryStatus() {
            try {
                const result = await apiCall('/api/xiuxian/ai_director/story_status/');
                showResult('ai-result', result);
            } catch (error) {
                showResult('ai-result', '错误: ' + error.message, true);
            }
        }

        async function testAIDirectorWorldState() {
            try {
                const result = await apiCall('/api/xiuxian/ai_director/world_state/');
                showResult('ai-result', result);
            } catch (error) {
                showResult('ai-result', '错误: ' + error.message, true);
            }
        }

        async function testAIDirectorUpdateContext() {
            try {
                const context = document.getElementById('ai-context').value;
                const result = await apiCall('/api/xiuxian/ai_director/update_context/', 'POST', {
                    context: context
                });
                showResult('ai-result', result);
            } catch (error) {
                showResult('ai-result', '错误: ' + error.message, true);
            }
        }

        // 性能测试
        async function runPerformanceTest() {
            showResult('performance-result', '正在运行性能测试...', false);
            
            const testCount = 50;
            const startTime = performance.now();
            
            try {
                const promises = [];
                for (let i = 0; i < testCount; i++) {
                    promises.push(apiCall('/api/xiuxian/cultivation/realm/'));
                }
                
                await Promise.all(promises);
                
                const endTime = performance.now();
                const totalTime = endTime - startTime;
                
                const result = `性能测试完成！
测试次数: ${testCount}
总耗时: ${totalTime.toFixed(2)}ms
平均响应时间: ${(totalTime / testCount).toFixed(2)}ms
QPS: ${(testCount / (totalTime / 1000)).toFixed(2)}`;
                
                showResult('performance-result', result);
            } catch (error) {
                showResult('performance-result', '性能测试失败: ' + error.message, true);
            }
        }

        // 运行所有测试
        async function runAllTests() {
            showResult('performance-result', '正在运行所有测试...', false);
            
            const tests = [
                testCultivationRealm,
                testCultivationProgress,
                testCombatAvailableSkills,
                testCombatLearnedSkills,
                testAlchemyRecipes,
                testAlchemyMaterials,
                testKarmaStatus,
                testAIDirectorStoryStatus,
                testAIDirectorWorldState
            ];
            
            try {
                for (const test of tests) {
                    await test();
                    await new Promise(resolve => setTimeout(resolve, 100)); // 短暂延迟
                }
                
                showResult('performance-result', '所有测试完成！请查看各个模块的测试结果。');
            } catch (error) {
                showResult('performance-result', '测试过程中出现错误: ' + error.message, true);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('TagProperty测试页面已加载');
        });
    </script>
</body>
</html>
