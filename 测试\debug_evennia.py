#!/usr/bin/env python
"""
调试Evennia导入问题
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_evennia():
    """调试Evennia导入"""
    print("调试Evennia导入...")
    
    # 设置Django环境
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
    
    try:
        import django
        django.setup()
        print("✓ Django设置成功")
    except Exception as e:
        print(f"✗ Django设置失败: {e}")
        return False
    
    try:
        import evennia
        print(f"✓ Evennia导入成功: {evennia}")
    except Exception as e:
        print(f"✗ Evennia导入失败: {e}")
        return False
    
    try:
        from evennia import DefaultScript
        print(f"✓ DefaultScript导入成功: {DefaultScript}")
        
        if DefaultScript is None:
            print("⚠ DefaultScript为None")
            return False
            
    except Exception as e:
        print(f"✗ DefaultScript导入失败: {e}")
        return False
    
    try:
        from evennia import logger
        print(f"✓ logger导入成功: {logger}")
    except Exception as e:
        print(f"✗ logger导入失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    debug_evennia()
