"""
Characters

Characters are (by default) Objects setup to be puppeted by Accounts.
They are what you "see" in game. The Character class in this module
is setup to be the "default" character type created by the default
creation commands.

"""

from evennia.objects.objects import <PERSON>faultCharacter
from mygame.systems.tagproperty_system import TagPropertyMixin

from .objects import ObjectParent


class Character(ObjectParent, DefaultCharacter, TagPropertyMixin):
    """
    The Character implements TagProperty high-performance query system
    for cultivation and other game mechanics.

    See mygame/typeclasses/objects.py for a list of
    properties and methods available on all Object child classes like this.

    """

    def at_object_creation(self):
        """
        Called only at the very first creation of the object.
        """
        super().at_object_creation()

        # 初始化TagProperty系统
        self.initialize_tagproperty_system()

        # 设置初始修仙境界
        self.cultivation.set_realm("练气期", 1)

        # 设置基础标签
        self.tags.set("character_type", "player")
        self.tags.set("created_at", self.date_created.timestamp())

        # 设置初始位置标签
        if self.location:
            self.tags.set("location", self.location.key)

    def initialize_tagproperty_system(self):
        """初始化TagProperty系统"""
        # TagPropertyMixin会自动创建tags和cultivation属性
        # 这里可以添加额外的初始化逻辑
        pass

    def advance_cultivation(self, points: int):
        """修仙进度推进"""
        self.cultivation.advance_cultivation(points)

        # 检查是否可以突破
        if self.cultivation.can_breakthrough():
            self.msg("你感觉到修为已经达到了突破的边缘...")

    def attempt_breakthrough(self):
        """尝试境界突破"""
        if not self.cultivation.can_breakthrough():
            self.msg("你的修为还不足以突破当前境界。")
            return False

        current_realm, current_level = self.cultivation.get_realm()
        realm_info = self.cultivation.realm_hierarchy[current_realm]

        # 突破逻辑
        if current_level < realm_info["max_level"]:
            # 提升层次
            new_level = current_level + 1
            self.cultivation.set_realm(current_realm, new_level)
            self.msg(f"恭喜！你成功突破到{current_realm}第{new_level}层！")
        else:
            # 提升境界
            realm_names = list(self.cultivation.realm_hierarchy.keys())
            current_index = realm_names.index(current_realm)

            if current_index < len(realm_names) - 1:
                next_realm = realm_names[current_index + 1]
                self.cultivation.set_realm(next_realm, 1)
                self.msg(f"恭喜！你成功突破到{next_realm}第1层！")
            else:
                self.msg("你已经达到了修仙的巅峰境界！")
                return False

        # 重置修为点数
        self.cultivation.set("cultivation_points", 0)
        self.cultivation.set("can_breakthrough", False)

        # 触发突破事件
        self.trigger_breakthrough_event(current_realm, current_level)

        return True

    def trigger_breakthrough_event(self, old_realm: str, old_level: int):
        """触发突破事件"""
        new_realm, new_level = self.cultivation.get_realm()

        # 向房间广播突破消息
        if self.location:
            self.location.msg_contents(
                f"{self.key}身上突然爆发出强大的灵力波动，成功突破到{new_realm}第{new_level}层！",
                exclude=[self]
            )

    def get_cultivation_display(self):
        """获取修仙信息显示"""
        progress = self.cultivation.get_cultivation_progress()

        display = f"""
|c修仙信息|n
境界: {progress['realm']} 第{progress['level']}层
修为点数: {progress['cultivation_points']}/{progress['required_points']}
进度: {progress['progress_percentage']:.1f}%
"""
        if progress['can_breakthrough']:
            display += "|g可以突破！|n"

        return display

    def move_to(self, destination, quiet=False, emit_to_obj=None, use_destination=True, to_none=False, move_hooks=True, **kwargs):
        """重写移动方法以更新位置标签"""
        result = super().move_to(destination, quiet, emit_to_obj, use_destination, to_none, move_hooks, **kwargs)

        # 更新位置标签
        if result and destination:
            self.tags.set("location", destination.key)

        return result
