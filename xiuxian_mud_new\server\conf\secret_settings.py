"""
This file is meant for when you want to share your game dir with
others but don't want to share all details of your specific game
or local server setup. The settings in this file will override those
in settings.py and is in .gitignore by default.

A good guideline when sharing your game dir is that you want your
game to run correctly also without this file and only use this
to override your public, shared settings.

"""

# The secret key is randomly seeded upon creation. It is used to sign
# Django's cookies and should not be publicly known. It should also
# generally not be changed once people have registered with the game
# since it will invalidate their existing sessions.
SECRET_KEY = '=aw/i`dByUVn]?MYS*)6JQ-vL_Fcjo&h|;48(PKR'

# PostgreSQL Database Configuration
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'xiuxian_mud',
        'USER': 'postgres',
        'PASSWORD': 'zy123good',
        'HOST': 'localhost',
        'PORT': '5432',
        'ATOMIC_REQUESTS': True,
        'CONN_MAX_AGE': 300,
    }
}

# Redis Cache Configuration  
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# AI Configuration (will be expanded as we develop)
# AI API Keys (to be added later)
# OPENAI_API_KEY = ""
# ANTHROPIC_API_KEY = ""

# 智匠MindCraft AI配置
MINDCRAFT_API_BASE = "https://api.mindcraft.com.cn/v1"
MINDCRAFT_API_KEY = "MC-94D4CC750E92436FB3FA51C9F41D03A9"
MINDCRAFT_MODEL = "deepseek-r1-free"

# AI导演配置
AI_SETTINGS = {
    'ENABLED': True,
    'PROVIDER': 'mindcraft',
    'BASE_URL': MINDCRAFT_API_BASE,
    'API_KEY': MINDCRAFT_API_KEY,
    'DEFAULT_MODEL': MINDCRAFT_MODEL,
    'MAX_TOKENS': 4000,
    'TEMPERATURE': 0.7,
    'STREAM': False,  # 游戏中不使用流式响应
}
