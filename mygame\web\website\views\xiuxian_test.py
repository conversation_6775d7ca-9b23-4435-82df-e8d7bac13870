#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修仙MUD测试页面视图
"""

from django.shortcuts import render
from django.http import HttpResponse

def xiuxian_test_page(request):
    """修仙Handler测试页面"""
    
    html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修仙MUD Handler测试界面</title>
    <style>
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            margin: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .handler-section { 
            background: white; 
            margin: 20px 0; 
            padding: 25px; 
            border-radius: 12px; 
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
        }
        .handler-title { 
            color: #333; 
            border-bottom: 2px solid #667eea; 
            padding-bottom: 15px; 
            margin-bottom: 20px;
            font-size: 1.4em;
        }
        .button { 
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white; 
            border: none; 
            padding: 12px 24px; 
            margin: 8px; 
            border-radius: 25px; 
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        .button:hover { 
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        .button:active {
            transform: translateY(0);
        }
        .result { 
            background: #f8f9fa; 
            border: 1px solid #e9ecef; 
            padding: 20px; 
            margin: 15px 0; 
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .input-group { 
            margin: 15px 0; 
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        .input-group label { 
            font-weight: bold; 
            color: #555;
            min-width: 80px;
        }
        .input-group input, .input-group select { 
            padding: 10px; 
            border: 2px solid #e9ecef; 
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        .status { 
            padding: 15px; 
            margin: 15px 0; 
            border-radius: 8px; 
            font-weight: bold;
        }
        .status.success { 
            background: #d4edda; 
            border: 1px solid #c3e6cb; 
            color: #155724; 
        }
        .status.error { 
            background: #f8d7da; 
            border: 1px solid #f5c6cb; 
            color: #721c24; 
        }
        .loading {
            display: none;
            text-align: center;
            color: #667eea;
            font-weight: bold;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧙‍♂️ 修仙MUD Handler测试界面</h1>
            <p>基于Evennia框架的修仙系统功能测试</p>
        </div>
        
        <div id="status" class="status success">系统已就绪，所有Handler加载完成</div>
        <div id="loading" class="loading">⏳ 正在处理请求...</div>
        
        <div class="grid">
            <!-- 修仙系统 -->
            <div class="handler-section">
                <h2 class="handler-title">🧘‍♂️ 修仙系统</h2>
                <button class="button" onclick="getCultivationRealm()">获取当前境界</button>
                <button class="button" onclick="getCultivationProgress()">获取修炼进度</button>
                <button class="button" onclick="startCultivation()">开始修炼</button>
                <button class="button" onclick="attemptBreakthrough()">尝试突破</button>
                <div id="cultivation-result" class="result">点击按钮测试修仙系统功能...</div>
            </div>
            
            <!-- 战斗技能系统 -->
            <div class="handler-section">
                <h2 class="handler-title">⚔️ 战斗技能系统</h2>
                <button class="button" onclick="getAvailableSkills()">获取可用技能</button>
                <button class="button" onclick="getLearnedSkills()">获取已学技能</button>
                <div class="input-group">
                    <label>技能名称:</label>
                    <input type="text" id="skill-name" value="火球术" placeholder="输入技能名称">
                    <button class="button" onclick="learnSkill()">学习技能</button>
                </div>
                <div id="combat-result" class="result">点击按钮测试战斗技能系统功能...</div>
            </div>
            
            <!-- 炼丹系统 -->
            <div class="handler-section">
                <h2 class="handler-title">🧪 炼丹系统</h2>
                <button class="button" onclick="getAlchemyRecipes()">获取配方</button>
                <button class="button" onclick="getMaterials()">获取材料</button>
                <div class="input-group">
                    <label>材料名称:</label>
                    <input type="text" id="material-name" value="灵石" placeholder="输入材料名称">
                    <label>数量:</label>
                    <input type="number" id="material-amount" value="5" placeholder="输入数量">
                    <button class="button" onclick="addMaterial()">添加材料</button>
                </div>
                <div id="alchemy-result" class="result">点击按钮测试炼丹系统功能...</div>
            </div>
            
            <!-- 因果系统 -->
            <div class="handler-section">
                <h2 class="handler-title">⚖️ 因果系统</h2>
                <button class="button" onclick="getKarmaStatus()">获取因果状态</button>
                <div class="input-group">
                    <label>行为类型:</label>
                    <select id="karma-type">
                        <option value="good">善行</option>
                        <option value="evil">恶行</option>
                    </select>
                    <label>数值:</label>
                    <input type="number" id="karma-amount" value="10" placeholder="输入数值">
                    <label>描述:</label>
                    <input type="text" id="karma-desc" value="帮助他人" placeholder="输入描述">
                    <button class="button" onclick="recordKarma()">记录因果</button>
                </div>
                <div id="karma-result" class="result">点击按钮测试因果系统功能...</div>
            </div>
            
            <!-- AI导演系统 -->
            <div class="handler-section">
                <h2 class="handler-title">🎭 AI导演系统</h2>
                <button class="button" onclick="getStoryStatus()">获取故事状态</button>
                <button class="button" onclick="getWorldState()">获取世界状态</button>
                <div class="input-group">
                    <label>上下文:</label>
                    <input type="text" id="context" value="角色开始修炼" placeholder="输入上下文">
                    <button class="button" onclick="updateContext()">更新上下文</button>
                </div>
                <div id="ai-director-result" class="result">点击按钮测试AI导演系统功能...</div>
            </div>
        </div>
    </div>

    <script>
        // 通用API调用函数
        async function callAPI(endpoint, data = null) {
            const loadingEl = document.getElementById('loading');
            const statusEl = document.getElementById('status');
            
            try {
                loadingEl.style.display = 'block';
                statusEl.className = 'status';
                statusEl.textContent = '正在处理请求...';
                
                const options = {
                    method: data ? 'POST' : 'GET',
                    headers: { 
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                };
                if (data) options.body = JSON.stringify(data);
                
                const response = await fetch('/api/xiuxian/' + endpoint, options);
                const result = await response.json();
                
                statusEl.className = result.success ? 'status success' : 'status error';
                statusEl.textContent = result.success ? '请求成功' : '请求失败: ' + (result.error || '未知错误');
                
                return result;
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = '网络错误: ' + error.message;
                return { success: false, error: error.message };
            } finally {
                loadingEl.style.display = 'none';
            }
        }
        
        // 获取CSRF Token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        
        // 显示结果
        function showResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.innerHTML = '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
        }
        
        // 修仙系统API
        async function getCultivationRealm() {
            const result = await callAPI('cultivation/realm/');
            showResult('cultivation-result', result);
        }
        
        async function getCultivationProgress() {
            const result = await callAPI('cultivation/progress/');
            showResult('cultivation-result', result);
        }
        
        async function startCultivation() {
            const result = await callAPI('cultivation/cultivate/', {});
            showResult('cultivation-result', result);
        }
        
        async function attemptBreakthrough() {
            const result = await callAPI('cultivation/breakthrough/', {});
            showResult('cultivation-result', result);
        }
        
        // 战斗技能系统API
        async function getAvailableSkills() {
            const result = await callAPI('combat/available-skills/');
            showResult('combat-result', result);
        }
        
        async function getLearnedSkills() {
            const result = await callAPI('combat/learned-skills/');
            showResult('combat-result', result);
        }
        
        async function learnSkill() {
            const skillName = document.getElementById('skill-name').value;
            const result = await callAPI('combat/learn-skill/', { skill_name: skillName });
            showResult('combat-result', result);
        }
        
        // 炼丹系统API
        async function getAlchemyRecipes() {
            const result = await callAPI('alchemy/recipes/');
            showResult('alchemy-result', result);
        }
        
        async function getMaterials() {
            const result = await callAPI('alchemy/materials/');
            showResult('alchemy-result', result);
        }
        
        async function addMaterial() {
            const name = document.getElementById('material-name').value;
            const amount = parseInt(document.getElementById('material-amount').value);
            const result = await callAPI('alchemy/add-material/', { name, amount });
            showResult('alchemy-result', result);
        }
        
        // 因果系统API
        async function getKarmaStatus() {
            const result = await callAPI('karma/status/');
            showResult('karma-result', result);
        }
        
        async function recordKarma() {
            const karmaType = document.getElementById('karma-type').value;
            const amount = parseInt(document.getElementById('karma-amount').value);
            const description = document.getElementById('karma-desc').value;
            const result = await callAPI('karma/record/', { 
                karma_type: karmaType, amount, description 
            });
            showResult('karma-result', result);
        }
        
        // AI导演系统API
        async function getStoryStatus() {
            const result = await callAPI('ai-director/story-status/');
            showResult('ai-director-result', result);
        }
        
        async function getWorldState() {
            const result = await callAPI('ai-director/world-state/');
            showResult('ai-director-result', result);
        }
        
        async function updateContext() {
            const context = document.getElementById('context').value;
            const result = await callAPI('ai-director/update-context/', { context });
            showResult('ai-director-result', result);
        }
    </script>
</body>
</html>
    """
    
    return HttpResponse(html_content)
