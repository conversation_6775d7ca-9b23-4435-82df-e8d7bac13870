|Portal| 2025-06-29 01:38:02 [..] Loaded.
|Portal| 2025-06-29 01:38:01 [..] Loading /home/<USER>/.local/lib/python3.12/site-packages/evennia/server/portal/portal.py...
|Portal| 2025-06-29 01:38:02 [..] Loaded.
|Portal| 2025-06-29 01:38:02 [..] twistd 23.10.0 (/usr/bin/python3.12 3.12.3) starting up.
|Portal| 2025-06-29 01:38:02 [..] reactor class: twisted.internet.epollreactor.EPollReactor.
|Portal| 2025-06-29 01:38:03 [..] AMP starting on 4006
|Portal| 2025-06-29 01:38:03 [..] Telnet starting on 4000
|Portal| 2025-06-29 01:38:03 [..] Websocket starting on 4002
|Portal| 2025-06-29 01:38:03 [..] Webserver-proxy starting on 4001
|Portal| 2025-06-29 01:38:03 [..] Portal starting server ... 
|Portal| 2025-06-29 01:38:08 [..] Portal starting server ... 
|Portal| 2025-06-29 01:39:29 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=44032)
|Portal| 2025-06-29 01:39:29 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=44038)
|Portal| 2025-06-29 01:39:29 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=44048)
|Portal| 2025-06-29 01:39:29 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=44062)
|Portal| 2025-06-29 01:39:32 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=44006)
|Portal| 2025-06-29 01:39:32 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=44022)
|Portal| 2025-06-29 02:31:24 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=50436)
|Portal| 2025-06-29 10:25:46 [..] Timing out client: IPv4Address(type='TCP', host='127.0.0.1', port=59098)
|Portal| 2025-06-29 10:39:10 [..] (TCP Port 4001 Closed)
|Portal| 2025-06-29 10:39:10 [..] (TCP Port 4002 Closed)
|Portal| 2025-06-29 10:39:10 [..] (TCP Port 4000 Closed)
|Portal| 2025-06-29 10:39:10 [..] (TCP Port 4006 Closed)
|Portal| 2025-06-29 10:39:10 [..] Main loop terminated.
|Portal| 2025-06-29 10:39:10 [..] Server Shut Down.
