#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TagProperty系统简单功能测试
不依赖Evennia环境的独立测试
"""

import sys
import os
import time
from collections import defaultdict

# 模拟Evennia环境
class MockEvenniaObject:
    def __init__(self, obj_id, key=None):
        self.id = obj_id
        self.key = key or f"TestObject_{obj_id}"
        self.attributes = MockAttributes()
        self.location = None
        self.date_created = MockDateTime()

class MockDateTime:
    def timestamp(self):
        return 1640995200.0

class MockAttributes:
    def __init__(self):
        self.data = {}
    
    def add(self, key, value, category=None):
        full_key = f"{category}_{key}" if category else key
        self.data[full_key] = value
    
    def get(self, key, default=None, category=None):
        full_key = f"{category}_{key}" if category else key
        return self.data.get(full_key, default)
    
    def has(self, key, category=None):
        full_key = f"{category}_{key}" if category else key
        return full_key in self.data
    
    def remove(self, key, category=None):
        full_key = f"{category}_{key}" if category else key
        self.data.pop(full_key, None)
    
    def all(self, category=None):
        if not category:
            return self.data
        prefix = f"{category}_"
        return {k: v for k, v in self.data.items() if k.startswith(prefix)}

# 简化的TagProperty实现用于测试
class SimpleTagProperty:
    def __init__(self, owner, category="default"):
        self.owner = owner
        self.category = category
        self._tags = {}
    
    def set(self, tag_name, value):
        self._tags[tag_name] = value
        return True
    
    def get(self, tag_name, default=None):
        return self._tags.get(tag_name, default)
    
    def has(self, tag_name):
        return tag_name in self._tags
    
    def remove(self, tag_name):
        self._tags.pop(tag_name, None)
    
    def all(self):
        return self._tags.copy()

class SimpleCultivationTagProperty(SimpleTagProperty):
    def __init__(self, owner):
        super().__init__(owner, "cultivation")
        self.realm_hierarchy = {
            "练气期": {"max_level": 12, "tier": 1},
            "筑基期": {"max_level": 9, "tier": 2},
            "金丹期": {"max_level": 9, "tier": 3},
            "元婴期": {"max_level": 9, "tier": 4},
            "化神期": {"max_level": 9, "tier": 5}
        }
    
    def set_realm(self, realm, level):
        if realm not in self.realm_hierarchy:
            return False
        
        max_level = self.realm_hierarchy[realm]["max_level"]
        if level < 1 or level > max_level:
            return False
        
        self.set("realm", realm)
        self.set("level", level)
        self.set("realm_tier", self.realm_hierarchy[realm]["tier"])
        self.set("realm_power", self.calculate_realm_power(realm, level))
        return True
    
    def get_realm(self):
        realm = self.get("realm", "练气期")
        level = self.get("level", 1)
        return realm, level
    
    def calculate_realm_power(self, realm, level):
        tier = self.realm_hierarchy[realm]["tier"]
        base_power = tier * 1000
        level_bonus = level * 100
        return base_power + level_bonus
    
    def advance_cultivation(self, points):
        current_points = self.get("cultivation_points", 0)
        new_points = current_points + points
        self.set("cultivation_points", new_points)
        
        # 检查是否可以突破
        if self.can_breakthrough():
            self.set("can_breakthrough", True)
    
    def can_breakthrough(self):
        realm, level = self.get_realm()
        cultivation_points = self.get("cultivation_points", 0)
        required_points = self.get_breakthrough_requirement(realm, level)
        return cultivation_points >= required_points
    
    def get_breakthrough_requirement(self, realm, level):
        tier = self.realm_hierarchy[realm]["tier"]
        base_requirement = 1000 * (tier ** 2)
        level_multiplier = level * 1.5
        return int(base_requirement * level_multiplier)

class SimpleTagPropertyMixin:
    def __init__(self):
        self._tags = SimpleTagProperty(self, "general")
        self._cultivation = SimpleCultivationTagProperty(self)
    
    @property
    def tags(self):
        return self._tags
    
    @property
    def cultivation(self):
        return self._cultivation

class TestCharacter(SimpleTagPropertyMixin):
    def __init__(self, obj_id, key=None):
        self.id = obj_id
        self.key = key or f"Character_{obj_id}"
        self.attributes = MockAttributes()
        self.location = None
        super().__init__()

def test_basic_tagproperty():
    """测试基础TagProperty功能"""
    print("测试基础TagProperty功能...")
    
    char = TestCharacter(1, "TestHero")
    
    # 测试设置和获取标签
    char.tags.set("name", "张三")
    char.tags.set("age", 25)
    char.tags.set("sect", "青云门")
    
    assert char.tags.get("name") == "张三"
    assert char.tags.get("age") == 25
    assert char.tags.get("sect") == "青云门"
    assert char.tags.get("nonexistent") is None
    
    # 测试has方法
    assert char.tags.has("name") == True
    assert char.tags.has("nonexistent") == False
    
    # 测试remove方法
    char.tags.remove("age")
    assert char.tags.has("age") == False
    
    # 测试all方法
    all_tags = char.tags.all()
    assert "name" in all_tags
    assert "sect" in all_tags
    assert "age" not in all_tags
    
    print("✅ 基础TagProperty功能测试通过")

def test_cultivation_system():
    """测试修仙系统功能"""
    print("测试修仙系统功能...")
    
    char = TestCharacter(2, "修仙者")
    
    # 测试设置境界
    result = char.cultivation.set_realm("筑基期", 3)
    assert result == True
    
    realm, level = char.cultivation.get_realm()
    assert realm == "筑基期"
    assert level == 3
    
    # 测试境界战力计算
    power = char.cultivation.get("realm_power")
    expected_power = 2 * 1000 + 3 * 100  # tier=2, level=3
    assert power == expected_power
    
    # 测试无效境界
    result = char.cultivation.set_realm("无效境界", 1)
    assert result == False
    
    # 测试无效层次
    result = char.cultivation.set_realm("练气期", 15)  # 超过最大层次
    assert result == False
    
    # 测试修为进度
    char.cultivation.advance_cultivation(5000)
    points = char.cultivation.get("cultivation_points")
    assert points == 5000
    
    # 测试突破检查
    char.cultivation.set("cultivation_points", 20000)  # 设置足够的修为点数
    can_breakthrough = char.cultivation.can_breakthrough()
    assert can_breakthrough == True
    
    print("✅ 修仙系统功能测试通过")

def test_performance_basic():
    """测试基础性能"""
    print("测试基础性能...")
    
    # 创建大量角色
    characters = []
    start_time = time.perf_counter()
    
    for i in range(1000):
        char = TestCharacter(i, f"Character_{i}")
        char.tags.set("type", "player")
        char.tags.set("level", i % 100)
        char.cultivation.set_realm("练气期", (i % 12) + 1)
        characters.append(char)
    
    creation_time = (time.perf_counter() - start_time) * 1000
    
    # 测试查询性能
    start_time = time.perf_counter()
    players = [char for char in characters if char.tags.get("type") == "player"]
    query_time = (time.perf_counter() - start_time) * 1000
    
    # 测试修仙查询性能
    start_time = time.perf_counter()
    high_level_chars = [char for char in characters 
                       if char.cultivation.get("realm_tier", 0) >= 1]
    cultivation_query_time = (time.perf_counter() - start_time) * 1000
    
    print(f"创建1000个角色耗时: {creation_time:.2f}ms")
    print(f"类型查询耗时: {query_time:.2f}ms")
    print(f"修仙查询耗时: {cultivation_query_time:.2f}ms")
    print(f"查询结果数量: {len(players)}")
    print(f"高级修仙者数量: {len(high_level_chars)}")
    
    # 验证性能（应该很快）
    assert creation_time < 1000  # 创建应该在1秒内
    assert query_time < 100     # 查询应该在100ms内
    assert cultivation_query_time < 100  # 修仙查询应该在100ms内
    
    print("✅ 基础性能测试通过")

def test_character_integration():
    """测试角色集成功能"""
    print("测试角色集成功能...")
    
    char = TestCharacter(3, "集成测试角色")
    
    # 测试初始化
    char.cultivation.set_realm("练气期", 1)
    char.tags.set("character_type", "player")
    char.tags.set("location", "青云山")
    
    # 测试修仙进度推进
    char.cultivation.advance_cultivation(3000)
    points = char.cultivation.get("cultivation_points")
    assert points == 3000
    
    # 测试突破检查
    char.cultivation.set("cultivation_points", 10000)  # 足够突破
    can_breakthrough = char.cultivation.can_breakthrough()
    assert can_breakthrough == True
    
    # 模拟突破过程
    if can_breakthrough:
        current_realm, current_level = char.cultivation.get_realm()
        realm_info = char.cultivation.realm_hierarchy[current_realm]
        
        if current_level < realm_info["max_level"]:
            # 提升层次
            new_level = current_level + 1
            char.cultivation.set_realm(current_realm, new_level)
        
        # 重置修为点数
        char.cultivation.set("cultivation_points", 0)
        char.cultivation.set("can_breakthrough", False)
    
    # 验证突破结果
    realm, level = char.cultivation.get_realm()
    assert level == 2  # 应该从1层提升到2层
    assert char.cultivation.get("cultivation_points") == 0
    
    print("✅ 角色集成功能测试通过")

def test_complex_scenarios():
    """测试复杂场景"""
    print("测试复杂场景...")
    
    # 创建多个不同境界的角色
    characters = []
    realms = ["练气期", "筑基期", "金丹期"]
    sects = ["青云门", "鬼王宗", "天音寺"]
    
    for i in range(30):
        char = TestCharacter(i + 100, f"复杂角色_{i}")
        
        # 设置随机属性
        realm = realms[i % len(realms)]
        sect = sects[i % len(sects)]
        level = (i % 9) + 1
        
        char.cultivation.set_realm(realm, level)
        char.tags.set("sect", sect)
        char.tags.set("karma", (i - 15) * 100)  # -1500 到 1400
        
        characters.append(char)
    
    # 复杂查询：青云门的筑基期弟子
    qingyun_zhuji = [char for char in characters 
                     if (char.tags.get("sect") == "青云门" and 
                         char.cultivation.get("realm") == "筑基期")]
    
    # 复杂查询：高战力角色
    high_power_chars = [char for char in characters 
                       if char.cultivation.get("realm_power", 0) > 2500]
    
    # 复杂查询：善良角色（karma > 0）
    good_chars = [char for char in characters 
                  if char.tags.get("karma", 0) > 0]
    
    print(f"青云门筑基期弟子数量: {len(qingyun_zhuji)}")
    print(f"高战力角色数量: {len(high_power_chars)}")
    print(f"善良角色数量: {len(good_chars)}")
    
    # 验证查询结果合理性
    assert len(qingyun_zhuji) >= 0
    assert len(high_power_chars) >= 0
    assert len(good_chars) >= 0
    
    print("✅ 复杂场景测试通过")

def main():
    """主测试函数"""
    print("=" * 60)
    print("TagProperty系统功能测试")
    print("=" * 60)
    
    try:
        test_basic_tagproperty()
        test_cultivation_system()
        test_performance_basic()
        test_character_integration()
        test_complex_scenarios()
        
        print("\n" + "=" * 60)
        print("🎉 所有功能测试通过！")
        print("✅ TagProperty系统功能正常")
        print("✅ 修仙系统集成成功")
        print("✅ 性能表现良好")
        print("✅ 复杂场景处理正确")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
