# 战斗技能Handler - 管理角色战斗技能和法术
# 基于设计文档实现的高性能战斗系统

import time
import random
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

try:
    from evennia import logger
    if logger is None:
        logger = None
except ImportError:
    logger = None
from ..handler_system import BaseHandler, handler_method, HandlerRegistry
from ..event_system import XianxiaEventBus, SkillCastEvent, CombatStateEvent


class SkillType(Enum):
    """技能类型"""
    ATTACK = "attack"      # 攻击技能
    DEFENSE = "defense"    # 防御技能
    UTILITY = "utility"    # 辅助技能
    MOVEMENT = "movement"  # 身法技能
    HEALING = "healing"    # 治疗技能


class ElementType(Enum):
    """元素类型"""
    FIRE = "fire"          # 火
    WATER = "water"        # 水
    EARTH = "earth"        # 土
    WOOD = "wood"          # 木
    METAL = "metal"        # 金
    LIGHTNING = "lightning" # 雷
    ICE = "ice"            # 冰
    WIND = "wind"          # 风
    NONE = "none"          # 无属性


@dataclass
class CombatSkill:
    """战斗技能数据类"""
    name: str
    skill_type: SkillType
    element: ElementType
    level: int
    max_level: int
    power: int
    cost: int  # 灵力消耗
    cooldown: float  # 冷却时间(秒)
    range: int  # 攻击范围
    description: str
    requirements: Dict[str, Any]
    effects: Dict[str, Any]


class CombatSkillHandler(BaseHandler):
    """
    战斗技能Handler - 管理角色的战斗技能、法术释放、技能升级等
    """
    
    def __init__(self, owner):
        # 必须在super().__init__之前设置所有属性
        # 技能库
        self.skill_library = {}

        # 角色技能
        self.character_skills = {}  # skill_name -> skill_data
        self.skill_cooldowns = {}   # skill_name -> cooldown_end_time

        # 战斗状态
        self.combat_state = {
            "in_combat": False,
            "combat_start_time": 0,
            "last_action_time": 0,
            "combo_count": 0,
            "current_target": None
        }

        # 事件总线
        self.event_bus = None

        # 加载默认技能
        self.load_default_skills()

        super().__init__(owner)
    
    def initialize(self):
        """初始化战斗技能Handler"""
        super().initialize()

        # 获取事件总线
        self.event_bus = XianxiaEventBus.get_instance()

        # 确保技能库已加载
        if not self.skill_library:
            self.load_default_skills()

        # 初始化基础技能
        self.learn_basic_skills()

        self.log_info("战斗技能Handler初始化完成")
    
    def load_default_skills(self):
        """加载默认技能库"""
        default_skills = [
            # 基础攻击技能
            CombatSkill(
                name="基础剑法",
                skill_type=SkillType.ATTACK,
                element=ElementType.NONE,
                level=1,
                max_level=10,
                power=100,
                cost=10,
                cooldown=1.0,
                range=1,
                description="基础的剑法攻击",
                requirements={"realm_tier": 1},
                effects={"damage_type": "physical"}
            ),
            
            # 火系法术
            CombatSkill(
                name="火球术",
                skill_type=SkillType.ATTACK,
                element=ElementType.FIRE,
                level=1,
                max_level=15,
                power=150,
                cost=20,
                cooldown=2.0,
                range=3,
                description="发射火球攻击敌人",
                requirements={"realm_tier": 2, "fire_affinity": 0.3},
                effects={"damage_type": "magical", "burn_chance": 0.2}
            ),
            
            CombatSkill(
                name="烈焰风暴",
                skill_type=SkillType.ATTACK,
                element=ElementType.FIRE,
                level=1,
                max_level=10,
                power=300,
                cost=50,
                cooldown=10.0,
                range=5,
                description="大范围火系攻击法术",
                requirements={"realm_tier": 4, "fire_affinity": 0.7},
                effects={"damage_type": "magical", "area_damage": True, "burn_duration": 5}
            ),
            
            # 防御技能
            CombatSkill(
                name="金钟罩",
                skill_type=SkillType.DEFENSE,
                element=ElementType.METAL,
                level=1,
                max_level=12,
                power=0,
                cost=30,
                cooldown=15.0,
                range=0,
                description="提升防御力的护体法术",
                requirements={"realm_tier": 3},
                effects={"defense_boost": 0.5, "duration": 30}
            ),
            
            # 身法技能
            CombatSkill(
                name="凌波微步",
                skill_type=SkillType.MOVEMENT,
                element=ElementType.WIND,
                level=1,
                max_level=8,
                power=0,
                cost=25,
                cooldown=8.0,
                range=0,
                description="提升移动速度和闪避率",
                requirements={"realm_tier": 2},
                effects={"speed_boost": 0.3, "dodge_boost": 0.2, "duration": 20}
            ),
            
            # 治疗技能
            CombatSkill(
                name="回春术",
                skill_type=SkillType.HEALING,
                element=ElementType.WOOD,
                level=1,
                max_level=10,
                power=200,
                cost=40,
                cooldown=5.0,
                range=1,
                description="恢复生命值的治疗法术",
                requirements={"realm_tier": 2, "wood_affinity": 0.4},
                effects={"heal_type": "instant", "heal_over_time": 10}
            )
        ]
        
        for skill in default_skills:
            self.skill_library[skill.name] = skill
    
    def learn_basic_skills(self):
        """学习基础技能"""
        owner = self.get_owner()
        if not owner:
            return
        
        # 所有角色都会基础剑法
        basic_skill = self.skill_library["基础剑法"]
        self.character_skills[basic_skill.name] = {
            "skill": basic_skill,
            "level": 1,
            "experience": 0,
            "learned_time": time.time()
        }
    
    @handler_method
    def learn_skill(self, skill_name: str) -> dict:
        """学习新技能"""
        if skill_name not in self.skill_library:
            return {"success": False, "message": f"技能 {skill_name} 不存在"}
        
        if skill_name in self.character_skills:
            return {"success": False, "message": f"已经学会技能 {skill_name}"}
        
        skill = self.skill_library[skill_name]
        
        # 检查学习要求
        if not self.check_skill_requirements(skill):
            return {"success": False, "message": f"不满足技能 {skill_name} 的学习要求"}
        
        # 学习技能
        self.character_skills[skill_name] = {
            "skill": skill,
            "level": 1,
            "experience": 0,
            "learned_time": time.time()
        }
        
        self.log_info(f"学会新技能: {skill_name}")
        
        return {
            "success": True,
            "message": f"成功学会技能: {skill_name}",
            "skill_info": self.get_skill_info(skill_name)
        }
    
    @handler_method
    def cast_skill(self, skill_name: str, target=None) -> dict:
        """释放技能"""
        if skill_name not in self.character_skills:
            return {"success": False, "message": f"未学会技能 {skill_name}"}
        
        # 检查冷却时间
        if not self.is_skill_ready(skill_name):
            remaining = self.get_skill_cooldown_remaining(skill_name)
            return {"success": False, "message": f"技能冷却中，剩余 {remaining:.1f} 秒"}
        
        skill_data = self.character_skills[skill_name]
        skill = skill_data["skill"]
        skill_level = skill_data["level"]
        
        owner = self.get_owner()
        if not owner:
            return {"success": False, "message": "角色不存在"}
        
        # 检查灵力消耗
        current_spiritual_energy = getattr(owner, 'cultivation', None)
        if current_spiritual_energy:
            spiritual_energy = current_spiritual_energy.get("spiritual_energy", 0)
            if spiritual_energy < skill.cost:
                return {"success": False, "message": f"灵力不足，需要 {skill.cost} 点"}
        
        # 计算技能效果
        base_power = skill.power
        level_bonus = (skill_level - 1) * 0.2  # 每级增加20%威力
        realm_bonus = self.get_realm_bonus()
        
        final_power = int(base_power * (1 + level_bonus + realm_bonus))
        
        # 消耗灵力
        if current_spiritual_energy:
            new_energy = spiritual_energy - skill.cost
            current_spiritual_energy.set("spiritual_energy", max(0, new_energy))
        
        # 设置冷却时间
        self.skill_cooldowns[skill_name] = time.time() + skill.cooldown
        
        # 增加技能经验
        self.add_skill_experience(skill_name, 1)
        
        # 更新战斗状态
        self.update_combat_state()
        
        result = {
            "success": True,
            "skill_name": skill_name,
            "skill_type": skill.skill_type.value,
            "element": skill.element.value,
            "power": final_power,
            "cost": skill.cost,
            "target": str(target) if target else None,
            "effects": skill.effects
        }
        
        self.log_info(f"释放技能: {skill_name}, 威力: {final_power}")
        
        # 发送技能释放事件
        if self.event_bus:
            event = SkillCastEvent(
                character_id=str(owner.id),
                skill_name=skill_name,
                skill_type=skill.skill_type.value,
                target_id=str(target.id) if target else None,
                power=final_power
            )
            self.event_bus.emit_event(event)
        
        return result
    
    @handler_method
    def upgrade_skill(self, skill_name: str) -> dict:
        """升级技能"""
        if skill_name not in self.character_skills:
            return {"success": False, "message": f"未学会技能 {skill_name}"}
        
        skill_data = self.character_skills[skill_name]
        skill = skill_data["skill"]
        current_level = skill_data["level"]
        current_exp = skill_data["experience"]
        
        if current_level >= skill.max_level:
            return {"success": False, "message": f"技能 {skill_name} 已达到最高等级"}
        
        # 计算升级所需经验
        required_exp = self.calculate_upgrade_experience(current_level)
        
        if current_exp < required_exp:
            return {
                "success": False, 
                "message": f"经验不足，需要 {required_exp} 点，当前 {current_exp} 点"
            }
        
        # 升级技能
        skill_data["level"] += 1
        skill_data["experience"] -= required_exp
        
        new_level = skill_data["level"]
        
        self.log_info(f"技能升级: {skill_name} {current_level} -> {new_level}")
        
        return {
            "success": True,
            "skill_name": skill_name,
            "old_level": current_level,
            "new_level": new_level,
            "remaining_exp": skill_data["experience"]
        }
    
    def check_skill_requirements(self, skill: CombatSkill) -> bool:
        """检查技能学习要求"""
        owner = self.get_owner()
        if not owner:
            return False
        
        # 检查境界要求
        if "realm_tier" in skill.requirements:
            required_tier = skill.requirements["realm_tier"]
            if hasattr(owner, 'cultivation'):
                current_tier = owner.cultivation.get("realm_tier", 1)
                if current_tier < required_tier:
                    return False
        
        # 检查元素亲和度要求
        for req_key, req_value in skill.requirements.items():
            if req_key.endswith("_affinity"):
                if hasattr(owner, 'tags'):
                    current_affinity = owner.tags.get(req_key, 0.0)
                    if current_affinity < req_value:
                        return False
        
        return True
    
    def is_skill_ready(self, skill_name: str) -> bool:
        """检查技能是否冷却完毕"""
        if skill_name not in self.skill_cooldowns:
            return True
        
        return time.time() >= self.skill_cooldowns[skill_name]
    
    def get_skill_cooldown_remaining(self, skill_name: str) -> float:
        """获取技能剩余冷却时间"""
        if skill_name not in self.skill_cooldowns:
            return 0.0
        
        remaining = self.skill_cooldowns[skill_name] - time.time()
        return max(0.0, remaining)
    
    def get_realm_bonus(self) -> float:
        """获取境界加成"""
        owner = self.get_owner()
        if owner and hasattr(owner, 'cultivation'):
            realm_tier = owner.cultivation.get("realm_tier", 1)
            return (realm_tier - 1) * 0.1  # 每个境界增加10%威力
        return 0.0
    
    def add_skill_experience(self, skill_name: str, exp: int):
        """增加技能经验"""
        if skill_name in self.character_skills:
            self.character_skills[skill_name]["experience"] += exp
    
    def calculate_upgrade_experience(self, current_level: int) -> int:
        """计算升级所需经验"""
        return current_level * 10  # 简单的线性增长
    
    def update_combat_state(self):
        """更新战斗状态"""
        current_time = time.time()
        
        if not self.combat_state["in_combat"]:
            self.combat_state["in_combat"] = True
            self.combat_state["combat_start_time"] = current_time
            self.combat_state["combo_count"] = 0
            
            # 发送进入战斗事件
            if self.event_bus:
                owner = self.get_owner()
                if owner:
                    event = CombatStateEvent(
                        character_id=str(owner.id),
                        state="enter_combat"
                    )
                    self.event_bus.emit_event(event)
        
        # 更新连击计数
        if current_time - self.combat_state["last_action_time"] < 5.0:
            self.combat_state["combo_count"] += 1
        else:
            self.combat_state["combo_count"] = 1
        
        self.combat_state["last_action_time"] = current_time
    
    @handler_method
    def get_learned_skills(self) -> List[dict]:
        """获取已学会的技能列表"""
        skills = []
        
        for skill_name, skill_data in self.character_skills.items():
            skill_info = self.get_skill_info(skill_name)
            skills.append(skill_info)
        
        return skills
    
    @handler_method
    def get_available_skills(self) -> dict:
        """获取可学习的技能列表"""
        available = {}

        for skill_name, skill in self.skill_library.items():
            if skill_name not in self.character_skills:
                can_learn = self.check_skill_requirements(skill)

                available[skill_name] = {
                    "name": skill.name,
                    "type": skill.skill_type.value,
                    "element": skill.element.value,
                    "power": skill.power,
                    "cost": skill.cost,
                    "cooldown": skill.cooldown,
                    "description": skill.description,
                    "can_learn": can_learn,
                    "requirements": skill.requirements
                }

        return available
    
    def get_skill_info(self, skill_name: str) -> dict:
        """获取技能详细信息"""
        if skill_name not in self.character_skills:
            return {}
        
        skill_data = self.character_skills[skill_name]
        skill = skill_data["skill"]
        
        return {
            "name": skill.name,
            "type": skill.skill_type.value,
            "element": skill.element.value,
            "level": skill_data["level"],
            "max_level": skill.max_level,
            "experience": skill_data["experience"],
            "power": skill.power,
            "cost": skill.cost,
            "cooldown": skill.cooldown,
            "range": skill.range,
            "description": skill.description,
            "effects": skill.effects,
            "is_ready": self.is_skill_ready(skill_name),
            "cooldown_remaining": self.get_skill_cooldown_remaining(skill_name)
        }
    
    @handler_method
    def get_combat_status(self) -> dict:
        """获取战斗状态"""
        status = self.combat_state.copy()
        
        if status["in_combat"]:
            status["combat_duration"] = time.time() - status["combat_start_time"]
        
        return status


# 注册Handler
HandlerRegistry.register(CombatSkillHandler, "combat")
