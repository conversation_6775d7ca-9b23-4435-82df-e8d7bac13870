# TagProperty高性能查询系统集成测试
# 验证所有功能正常工作

import sys
import os
import unittest
from unittest.mock import Mock, patch

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from mygame.systems.tagproperty_system import (
    TagProperty, TagIndexManager, TagQuery, 
    CultivationTagProperty, AIDirectorQueryInterface, TagPropertyMixin
)


class MockEvenniaObject:
    """模拟Evennia对象"""
    
    def __init__(self, obj_id: int, key: str = None):
        self.id = obj_id
        self.key = key or f"TestObject_{obj_id}"
        self.attributes = MockAttributes()
        self.location = None
        self.date_created = Mock()
        self.date_created.timestamp.return_value = 1640995200.0  # 2022-01-01


class MockAttributes:
    """模拟Evennia Attributes系统"""
    
    def __init__(self):
        self.data = {}
    
    def add(self, key: str, value, category: str = None):
        full_key = f"{category}_{key}" if category else key
        self.data[full_key] = value
    
    def get(self, key: str, default=None, category: str = None):
        full_key = f"{category}_{key}" if category else key
        return self.data.get(full_key, default)
    
    def has(self, key: str, category: str = None):
        full_key = f"{category}_{key}" if category else key
        return full_key in self.data
    
    def remove(self, key: str, category: str = None):
        full_key = f"{category}_{key}" if category else key
        self.data.pop(full_key, None)
    
    def all(self, category: str = None):
        if not category:
            return {k: v for k, v in self.data.items()}
        
        prefix = f"{category}_"
        result = {}
        for k, v in self.data.items():
            if k.startswith(prefix):
                result[k] = Mock()
                result[k].key = k
                result[k].value = v
        return result


class TestTagProperty(unittest.TestCase):
    """TagProperty基础功能测试"""
    
    def setUp(self):
        self.obj = MockEvenniaObject(1, "TestCharacter")
        self.tags = TagProperty(self.obj, "test")
    
    def test_set_and_get_tag(self):
        """测试设置和获取标签"""
        self.tags.set("test_tag", "test_value")
        self.assertEqual(self.tags.get("test_tag"), "test_value")
    
    def test_has_tag(self):
        """测试检查标签存在"""
        self.tags.set("exists", True)
        self.assertTrue(self.tags.has("exists"))
        self.assertFalse(self.tags.has("not_exists"))
    
    def test_remove_tag(self):
        """测试移除标签"""
        self.tags.set("to_remove", "value")
        self.assertTrue(self.tags.has("to_remove"))
        
        self.tags.remove("to_remove")
        self.assertFalse(self.tags.has("to_remove"))
    
    def test_all_tags(self):
        """测试获取所有标签"""
        self.tags.set("tag1", "value1")
        self.tags.set("tag2", "value2")
        
        all_tags = self.tags.all()
        self.assertIn("tag1", all_tags)
        self.assertIn("tag2", all_tags)
        self.assertEqual(all_tags["tag1"], "value1")
        self.assertEqual(all_tags["tag2"], "value2")


class TestCultivationTagProperty(unittest.TestCase):
    """修仙TagProperty测试"""
    
    def setUp(self):
        self.obj = MockEvenniaObject(2, "CultivationCharacter")
        self.cultivation = CultivationTagProperty(self.obj)
    
    def test_set_realm(self):
        """测试设置境界"""
        result = self.cultivation.set_realm("筑基期", 3)
        self.assertTrue(result)
        
        realm, level = self.cultivation.get_realm()
        self.assertEqual(realm, "筑基期")
        self.assertEqual(level, 3)
    
    def test_invalid_realm(self):
        """测试无效境界"""
        result = self.cultivation.set_realm("无效境界", 1)
        self.assertFalse(result)
    
    def test_invalid_level(self):
        """测试无效层次"""
        result = self.cultivation.set_realm("练气期", 15)  # 超过最大层次
        self.assertFalse(result)
    
    def test_realm_power_calculation(self):
        """测试境界战力计算"""
        self.cultivation.set_realm("金丹期", 5)
        power = self.cultivation.get("realm_power")
        expected_power = 3 * 1000 + 5 * 100  # tier=3, level=5
        self.assertEqual(power, expected_power)
    
    def test_cultivation_advancement(self):
        """测试修为进度"""
        self.cultivation.set_realm("练气期", 1)
        self.cultivation.advance_cultivation(5000)
        
        points = self.cultivation.get("cultivation_points")
        self.assertEqual(points, 5000)
    
    def test_breakthrough_check(self):
        """测试突破检查"""
        self.cultivation.set_realm("练气期", 1)
        self.cultivation.set("cultivation_points", 10000)  # 足够的修为点数
        
        can_breakthrough = self.cultivation.can_breakthrough()
        self.assertTrue(can_breakthrough)


class TestTagIndexManager(unittest.TestCase):
    """索引管理器测试"""
    
    def setUp(self):
        self.index_manager = TagIndexManager()
    
    def test_add_and_query_tag(self):
        """测试添加和查询标签"""
        self.index_manager.add_tag("obj1", "sect", "青云门", "test")
        self.index_manager.add_tag("obj2", "sect", "青云门", "test")
        self.index_manager.add_tag("obj3", "sect", "鬼王宗", "test")
        
        # 查询青云门弟子
        result = self.index_manager.query_by_value("青云门")
        self.assertEqual(len(result), 2)
        self.assertIn("obj1", result)
        self.assertIn("obj2", result)
    
    def test_range_query(self):
        """测试范围查询"""
        self.index_manager.add_tag("obj1", "power", 1500, "test")
        self.index_manager.add_tag("obj2", "power", 2500, "test")
        self.index_manager.add_tag("obj3", "power", 3500, "test")
        
        # 查询战力在2000-3000之间的对象
        result = self.index_manager.query_by_range("power", 2000, 3000)
        self.assertEqual(len(result), 1)
        self.assertIn("obj2", result)
    
    def test_category_query(self):
        """测试分类查询"""
        self.index_manager.add_tag("obj1", "type", "player", "characters")
        self.index_manager.add_tag("obj2", "type", "npc", "characters")
        
        # 查询角色分类下的所有对象
        result = self.index_manager.query_by_category("characters")
        self.assertEqual(len(result), 2)


class TestAIDirectorQueryInterface(unittest.TestCase):
    """AI导演查询接口测试"""
    
    def setUp(self):
        self.index_manager = TagIndexManager()
        self.ai_query = AIDirectorQueryInterface(self.index_manager)
        
        # 设置测试数据
        self.setup_test_data()
    
    def setup_test_data(self):
        """设置测试数据"""
        # 角色1：练气期弟子
        self.index_manager.add_tag("char1", "realm", "练气期", "cultivation")
        self.index_manager.add_tag("char1", "level", 5, "cultivation")
        self.index_manager.add_tag("char1", "realm_tier", 1, "cultivation")
        self.index_manager.add_tag("char1", "realm_power", 1500, "cultivation")
        self.index_manager.add_tag("char1", "location", "青云山", "general")
        self.index_manager.add_tag("char1", "sect", "青云门", "general")
        
        # 角色2：筑基期长老
        self.index_manager.add_tag("char2", "realm", "筑基期", "cultivation")
        self.index_manager.add_tag("char2", "level", 8, "cultivation")
        self.index_manager.add_tag("char2", "realm_tier", 2, "cultivation")
        self.index_manager.add_tag("char2", "realm_power", 2800, "cultivation")
        self.index_manager.add_tag("char2", "location", "青云山", "general")
        self.index_manager.add_tag("char2", "sect", "青云门", "general")
        self.index_manager.add_tag("char2", "can_breakthrough", True, "cultivation")
    
    def test_find_characters_by_realm(self):
        """测试按境界查找角色"""
        result = self.ai_query.find_characters_by_realm("练气期")
        self.assertIn("char1", result)
        self.assertNotIn("char2", result)
    
    def test_find_characters_in_realm_range(self):
        """测试按境界范围查找角色"""
        result = self.ai_query.find_characters_in_realm_range(1, 2)
        self.assertEqual(len(result), 2)
        self.assertIn("char1", result)
        self.assertIn("char2", result)
    
    def test_find_powerful_characters(self):
        """测试查找强大角色"""
        result = self.ai_query.find_powerful_characters(2000)
        self.assertEqual(len(result), 1)
        self.assertIn("char2", result)
    
    def test_find_breakthrough_candidates(self):
        """测试查找突破候选人"""
        result = self.ai_query.find_breakthrough_candidates()
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["character_id"], "char2")
    
    def test_get_narrative_context(self):
        """测试获取叙事上下文"""
        context = self.ai_query.get_narrative_context("char1")
        
        self.assertEqual(context["realm"], "练气期")
        self.assertEqual(context["level"], 5)
        self.assertEqual(context["location"], "青云山")
        self.assertEqual(context["sect"], "青云门")
    
    def test_suggest_story_events(self):
        """测试故事事件推荐"""
        suggestions = self.ai_query.suggest_story_events("char2")
        
        # 应该有突破机会事件
        breakthrough_events = [s for s in suggestions if s["type"] == "breakthrough_opportunity"]
        self.assertTrue(len(breakthrough_events) > 0)
    
    def test_find_conflict_potential(self):
        """测试冲突潜力分析"""
        # 添加敌对门派角色
        self.index_manager.add_tag("char3", "location", "青云山", "general")
        self.index_manager.add_tag("char3", "sect", "鬼王宗", "general")
        
        conflict_info = self.ai_query.find_conflict_potential("青云山")
        
        self.assertGreater(conflict_info["total_players"], 0)
        self.assertIn("青云门", conflict_info["sect_distribution"])


class TestTagPropertyMixin(unittest.TestCase):
    """TagPropertyMixin测试"""
    
    def setUp(self):
        class TestCharacter(TagPropertyMixin):
            def __init__(self, obj_id):
                self.id = obj_id
                self.key = f"Character_{obj_id}"
                self.attributes = MockAttributes()
                super().__init__()
        
        self.character = TestCharacter(1)
    
    def test_tags_property(self):
        """测试tags属性"""
        self.character.tags.set("test", "value")
        self.assertEqual(self.character.tags.get("test"), "value")
    
    def test_cultivation_property(self):
        """测试cultivation属性"""
        result = self.character.cultivation.set_realm("筑基期", 2)
        self.assertTrue(result)
        
        realm, level = self.character.cultivation.get_realm()
        self.assertEqual(realm, "筑基期")
        self.assertEqual(level, 2)
    
    def test_get_tagproperty(self):
        """测试获取指定分类的TagProperty"""
        location_tags = self.character.get_tagproperty("location")
        location_tags.set("current", "青云山")
        
        self.assertEqual(location_tags.get("current"), "青云山")


class TestPerformanceBasics(unittest.TestCase):
    """基础性能测试"""
    
    def setUp(self):
        self.index_manager = TagIndexManager()
        self.query = TagQuery(self.index_manager)
    
    def test_large_dataset_performance(self):
        """测试大数据集性能"""
        import time
        
        # 添加1000个对象
        start_time = time.perf_counter()
        for i in range(1000):
            self.index_manager.add_tag(f"obj{i}", "type", "test", "performance")
            self.index_manager.add_tag(f"obj{i}", "value", i, "performance")
        add_time = time.perf_counter() - start_time
        
        # 查询性能
        start_time = time.perf_counter()
        result = self.query.find_by_value("test")
        query_time = time.perf_counter() - start_time
        
        # 范围查询性能
        start_time = time.perf_counter()
        range_result = self.query.find_by_range("value", 100, 200)
        range_query_time = time.perf_counter() - start_time
        
        # 验证结果
        self.assertEqual(len(result), 1000)
        self.assertEqual(len(range_result), 101)  # 100-200 inclusive
        
        # 性能断言（应该很快）
        self.assertLess(add_time, 1.0)  # 添加1000个对象应该在1秒内
        self.assertLess(query_time, 0.01)  # 查询应该在10ms内
        self.assertLess(range_query_time, 0.01)  # 范围查询应该在10ms内
        
        print(f"添加1000个对象耗时: {add_time*1000:.2f}ms")
        print(f"值查询耗时: {query_time*1000:.2f}ms")
        print(f"范围查询耗时: {range_query_time*1000:.2f}ms")


if __name__ == "__main__":
    # 运行所有测试
    unittest.main(verbosity=2)
