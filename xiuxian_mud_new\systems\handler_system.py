# Handler生态组件化框架
# 基于设计文档实现的@lazy_property模式和70%+内存优化

import time
import threading
import weakref
from functools import wraps
from typing import Dict, List, Any, Optional, Type, Callable
from collections import defaultdict

try:
    from evennia import logger
    if logger is None:
        logger = None
except ImportError:
    logger = None


class lazy_property:
    """
    延迟加载属性装饰器
    只在首次访问时创建对象，并提供自动回收机制
    """
    
    def __init__(self, func):
        self.func = func
        self.name = func.__name__
        self.cache_key = f"_lazy_{self.name}"
        
    def __get__(self, obj, cls):
        if obj is None:
            return self
        
        # 检查缓存
        if hasattr(obj, self.cache_key):
            cached_value = getattr(obj, self.cache_key)
            if cached_value is not None:
                return cached_value
        
        # 创建新实例
        value = self.func(obj)
        setattr(obj, self.cache_key, value)
        
        # 注册到内存管理器
        HandlerMemoryManager.register_handler(obj, self.name, value)
        
        return value
    
    def __set__(self, obj, value):
        setattr(obj, self.cache_key, value)
    
    def __delete__(self, obj):
        if hasattr(obj, self.cache_key):
            # 清理Handler
            old_value = getattr(obj, self.cache_key)
            if hasattr(old_value, 'cleanup'):
                old_value.cleanup()
            
            delattr(obj, self.cache_key)
            HandlerMemoryManager.unregister_handler(obj, self.name)


class BaseHandler:
    """
    Handler基类 - 所有功能模块的基础
    """
    
    def __init__(self, owner):
        self.owner = weakref.ref(owner)  # 弱引用避免循环引用
        self.is_active = False
        self.last_access_time = time.time()
        self.initialization_data = {}
        self.dependencies = []
        self.dependents = []
        
        # 性能统计
        self.stats = {
            "access_count": 0,
            "memory_usage": 0,
            "last_cleanup": time.time()
        }
        
        # 初始化Handler
        self.initialize()
    
    def initialize(self):
        """初始化Handler - 子类可重写"""
        self.is_active = True
        self.log_info(f"{self.__class__.__name__} initialized")
    
    def cleanup(self):
        """清理Handler资源"""
        self.is_active = False
        self.clear_dependencies()
        self.log_info(f"{self.__class__.__name__} cleaned up")
    
    def access(self):
        """记录访问时间和统计"""
        self.last_access_time = time.time()
        self.stats["access_count"] += 1
    
    def add_dependency(self, handler_name: str):
        """添加依赖关系"""
        if handler_name not in self.dependencies:
            self.dependencies.append(handler_name)
            
            # 通知依赖的Handler
            owner = self.owner()
            if owner and hasattr(owner, f"get_handler"):
                dependent_handler = owner.get_handler(handler_name)
                if dependent_handler and self not in dependent_handler.dependents:
                    dependent_handler.dependents.append(self)
    
    def clear_dependencies(self):
        """清理依赖关系"""
        for dependent in self.dependents:
            try:
                dependent.remove_dependency(self.__class__.__name__.lower())
            except:
                pass
        
        self.dependencies.clear()
        self.dependents.clear()
    
    def get_owner(self):
        """获取拥有者对象"""
        return self.owner()
    
    def log_info(self, message: str):
        """日志记录"""
        owner = self.get_owner()
        owner_name = owner.key if owner else "Unknown"
        log_msg = f"[Handler] {owner_name}.{self.__class__.__name__}: {message}"
        if logger:
            logger.log_info(log_msg)
        else:
            print(log_msg)
    
    def serialize_state(self) -> dict:
        """序列化Handler状态"""
        return {
            "class_name": self.__class__.__name__,
            "is_active": self.is_active,
            "initialization_data": self.initialization_data,
            "dependencies": self.dependencies,
            "stats": self.stats
        }
    
    def deserialize_state(self, state_data: dict):
        """反序列化Handler状态"""
        self.is_active = state_data.get("is_active", False)
        self.initialization_data = state_data.get("initialization_data", {})
        self.dependencies = state_data.get("dependencies", [])
        self.stats = state_data.get("stats", {})


class HandlerRegistry:
    """
    Handler注册中心 - 管理所有可用的Handler类型
    """
    
    _handlers: Dict[str, Type[BaseHandler]] = {}
    _categories: Dict[str, List[str]] = defaultdict(list)
    
    @classmethod
    def register(cls, handler_class: Type[BaseHandler], category: str = "general"):
        """注册Handler类"""
        handler_name = handler_class.__name__.lower()
        cls._handlers[handler_name] = handler_class
        cls._categories[category].append(handler_name)
        
        log_msg = f"Handler registered: {handler_name} in category '{category}'"
        if logger:
            logger.log_info(log_msg)
        else:
            print(log_msg)
    
    @classmethod
    def get_handler_class(cls, handler_name: str) -> Optional[Type[BaseHandler]]:
        """获取Handler类"""
        return cls._handlers.get(handler_name.lower())
    
    @classmethod
    def get_available_handlers(cls, category: str = None) -> List[str]:
        """获取可用的Handler列表"""
        if category:
            return cls._categories.get(category, [])
        return list(cls._handlers.keys())
    
    @classmethod
    def create_handler(cls, handler_name: str, owner) -> Optional[BaseHandler]:
        """创建Handler实例"""
        handler_class = cls.get_handler_class(handler_name)
        if handler_class:
            return handler_class(owner)
        return None


class HandlerMemoryManager:
    """
    Handler内存管理器 - 自动回收和优化内存使用
    """
    
    _active_handlers: Dict[int, Dict[str, BaseHandler]] = defaultdict(dict)
    _cleanup_threshold = 300  # 5分钟无访问则考虑清理
    _memory_pressure_threshold = 0.8  # 内存压力阈值
    
    @classmethod
    def register_handler(cls, owner, handler_name: str, handler: BaseHandler):
        """注册Handler到内存管理"""
        owner_id = id(owner)
        cls._active_handlers[owner_id][handler_name] = handler
    
    @classmethod
    def unregister_handler(cls, owner, handler_name: str):
        """从内存管理中注销Handler"""
        owner_id = id(owner)
        if owner_id in cls._active_handlers:
            cls._active_handlers[owner_id].pop(handler_name, None)
            
            # 如果没有Handler了，清理整个条目
            if not cls._active_handlers[owner_id]:
                del cls._active_handlers[owner_id]
    
    @classmethod
    def cleanup_inactive_handlers(cls):
        """清理不活跃的Handler"""
        current_time = time.time()
        cleanup_count = 0
        
        for owner_id, handlers in list(cls._active_handlers.items()):
            for handler_name, handler in list(handlers.items()):
                # 检查是否超过清理阈值
                if (current_time - handler.last_access_time) > cls._cleanup_threshold:
                    # 检查是否有依赖
                    if not handler.dependents:
                        handler.cleanup()
                        del handlers[handler_name]
                        cleanup_count += 1
            
            # 清理空的owner条目
            if not handlers:
                del cls._active_handlers[owner_id]
        
        if cleanup_count > 0:
            log_msg = f"Cleaned up {cleanup_count} inactive handlers"
            if logger:
                logger.log_info(log_msg)
            else:
                print(log_msg)
        
        return cleanup_count
    
    @classmethod
    def get_memory_stats(cls) -> dict:
        """获取内存使用统计"""
        total_handlers = sum(len(handlers) for handlers in cls._active_handlers.values())
        total_owners = len(cls._active_handlers)
        
        return {
            "total_handlers": total_handlers,
            "total_owners": total_owners,
            "average_handlers_per_owner": total_handlers / max(total_owners, 1),
            "active_owners": list(cls._active_handlers.keys())
        }


class HandlerMixin:
    """
    为Evennia对象提供Handler功能的Mixin
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._handler_states = {}
    
    def get_handler(self, handler_name: str) -> Optional[BaseHandler]:
        """获取指定的Handler"""
        # 尝试从lazy_property获取
        if hasattr(self, handler_name):
            handler = getattr(self, handler_name)
            if isinstance(handler, BaseHandler):
                handler.access()
                return handler
        
        # 尝试动态创建
        handler = HandlerRegistry.create_handler(handler_name, self)
        if handler:
            setattr(self, f"_lazy_{handler_name}", handler)
            return handler
        
        return None
    
    def activate_handler(self, handler_name: str, **initialization_data):
        """激活指定的Handler"""
        handler = self.get_handler(handler_name)
        if handler:
            handler.initialization_data.update(initialization_data)
            handler.initialize()
            return True
        return False
    
    def deactivate_handler(self, handler_name: str):
        """停用指定的Handler"""
        handler = self.get_handler(handler_name)
        if handler:
            handler.cleanup()
            # 从lazy_property缓存中移除
            cache_key = f"_lazy_{handler_name}"
            if hasattr(self, cache_key):
                delattr(self, cache_key)
            return True
        return False
    
    def list_active_handlers(self) -> List[str]:
        """列出活跃的Handler"""
        active_handlers = []
        
        # 检查所有可能的lazy_property
        for attr_name in dir(self):
            if attr_name.startswith("_lazy_"):
                handler_name = attr_name[6:]  # 移除 "_lazy_" 前缀
                handler = getattr(self, attr_name, None)
                if handler and isinstance(handler, BaseHandler) and handler.is_active:
                    active_handlers.append(handler_name)
        
        return active_handlers
    
    def save_handler_states(self):
        """保存Handler状态"""
        states = {}
        for handler_name in self.list_active_handlers():
            handler = self.get_handler(handler_name)
            if handler:
                states[handler_name] = handler.serialize_state()
        
        if hasattr(self, 'attributes'):
            self.attributes.add("handler_states", states, category="system")
    
    def restore_handler_states(self):
        """恢复Handler状态"""
        if hasattr(self, 'attributes'):
            states = self.attributes.get("handler_states", {}, category="system")
            
            for handler_name, state_data in states.items():
                if self.activate_handler(handler_name):
                    handler = self.get_handler(handler_name)
                    if handler:
                        handler.deserialize_state(state_data)


# 装饰器用于Handler方法
def handler_method(func):
    """标记Handler方法的装饰器"""
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        if not self.is_active:
            self.log_info("Handler is not active, activating...")
            self.initialize()
        
        self.access()
        return func(self, *args, **kwargs)
    
    return wrapper
