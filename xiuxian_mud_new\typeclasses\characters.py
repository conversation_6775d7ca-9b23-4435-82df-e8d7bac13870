"""
Characters

Characters are (by default) Objects setup to be puppeted by Accounts.
They are what you "see" in game. The Character class in this module
is setup to be the "default" character type created by the default
creation commands.

"""

from evennia.objects.objects import DefaultCharacter

from .objects import ObjectParent
from systems.tagproperty_system import TagPropertyMixin
from systems.handler_system import HandlerMixin, lazy_property


class Character(ObjectParent, DefaultCharacter, TagPropertyMixin, HandlerMixin):
    """
    修仙MUD角色类 - 集成所有Day3-4系统

    继承自ObjectParent, DefaultCharacter, TagPropertyMixin, HandlerMixin
    提供完整的修仙游戏功能：修炼、战斗、炼丹、因果、AI导演
    """

    def at_object_creation(self):
        """角色创建时的初始化"""
        super().at_object_creation()

        # 初始化修仙属性
        self.initialize_cultivation_attributes()

        # 初始化TagProperty系统
        self.initialize_tagproperty()

        # 初始化Handler系统
        self.initialize_handlers()

    def initialize_cultivation_attributes(self):
        """初始化修仙相关属性"""
        # 基础修仙属性
        if not self.cultivation.has("realm"):
            self.cultivation.set_realm("练气期", 1)

        if not self.cultivation.has("cultivation_points"):
            self.cultivation.set("cultivation_points", 0)

        if not self.cultivation.has("spiritual_energy"):
            self.cultivation.set("spiritual_energy", 100)

        if not self.cultivation.has("max_spiritual_energy"):
            self.cultivation.set("max_spiritual_energy", 100)

        # 元素亲和度
        elements = ["fire_affinity", "water_affinity", "earth_affinity",
                   "wood_affinity", "metal_affinity"]
        for element in elements:
            if not self.tags.has(element):
                # 随机初始亲和度 0.1-0.3
                import random
                affinity = random.uniform(0.1, 0.3)
                self.tags.set(element, affinity)

    def initialize_tagproperty(self):
        """初始化TagProperty系统"""
        # 设置角色基础标签
        self.tags.set("character_type", "player")
        self.tags.set("creation_time", self.date_created.timestamp())

        # 设置修仙相关标签
        realm, level = self.cultivation.get_realm()
        self.tags.set("current_realm", realm)
        self.tags.set("current_level", level)

        # 计算境界层级
        realm_tiers = {
            "练气期": 1, "筑基期": 2, "金丹期": 3, "元婴期": 4,
            "化神期": 5, "炼虚期": 6, "合体期": 7, "大乘期": 8, "渡劫期": 9
        }
        tier = realm_tiers.get(realm, 1)
        self.cultivation.set("realm_tier", tier)
        self.tags.set("realm_tier", tier)

    def initialize_handlers(self):
        """初始化Handler系统"""
        # Handler会通过@lazy_property自动初始化
        # 这里只需要确保基础设置正确
        pass

    # Handler属性 - 使用@lazy_property实现延迟加载

    @lazy_property
    def cultivation(self):
        """修仙Handler - 管理修炼、突破、功法等"""
        from systems.handlers import CultivationHandler
        return CultivationHandler(self)

    @lazy_property
    def combat(self):
        """战斗技能Handler - 管理技能、法术、战斗等"""
        from systems.handlers import CombatSkillHandler
        return CombatSkillHandler(self)

    @lazy_property
    def alchemy(self):
        """炼丹Handler - 管理炼丹、配方、材料等"""
        from systems.handlers import AlchemyHandler
        return AlchemyHandler(self)

    @lazy_property
    def karma(self):
        """因果Handler - 管理善恶业力、天道感应等"""
        from systems.handlers import KarmaHandler
        return KarmaHandler(self)

    @lazy_property
    def ai_director(self):
        """AI导演Handler - 管理剧情生成、世界事件等"""
        from systems.handlers import AIDirectorHandler
        return AIDirectorHandler(self)

    # 便捷方法

    def get_cultivation_info(self):
        """获取修炼信息"""
        realm, level = self.cultivation.get_current_realm()
        progress = self.cultivation.get_cultivation_progress()

        return {
            "realm": f"{realm}第{level}层",
            "cultivation_points": progress.get("cultivation_points", 0),
            "spiritual_energy": self.cultivation.get("spiritual_energy", 0),
            "can_breakthrough": progress.get("can_breakthrough", False)
        }

    def get_combat_info(self):
        """获取战斗信息"""
        skills = self.combat.get_learned_skills()
        status = self.combat.get_combat_status()

        return {
            "learned_skills": len(skills),
            "in_combat": status.get("in_combat", False),
            "combo_count": status.get("combo_count", 0)
        }

    def get_alchemy_info(self):
        """获取炼丹信息"""
        status = self.alchemy.get_alchemy_status()

        return {
            "alchemy_level": status.get("alchemy_level", 1),
            "known_recipes": status.get("known_recipes_count", 0),
            "pills_count": status.get("pills_count", 0),
            "is_refining": status.get("is_refining", False)
        }

    def get_karma_info(self):
        """获取因果信息"""
        status = self.karma.get_karma_status()

        return {
            "karma_balance": status.get("karma_balance", 0),
            "karma_level": status.get("karma_level", "普通"),
            "heavenly_attention": status.get("heavenly_attention", 0)
        }

    def get_full_status(self):
        """获取完整状态信息"""
        return {
            "basic_info": {
                "name": self.name,
                "location": str(self.location) if self.location else "未知"
            },
            "cultivation": self.get_cultivation_info(),
            "combat": self.get_combat_info(),
            "alchemy": self.get_alchemy_info(),
            "karma": self.get_karma_info()
        }
