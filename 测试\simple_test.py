#!/usr/bin/env python
"""
简单的系统测试
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """测试基础导入"""
    print("测试基础Python模块...")
    
    try:
        from dataclasses import dataclass, field
        from enum import Enum
        import time
        import uuid
        print("✓ 基础模块导入成功")
    except Exception as e:
        print(f"✗ 基础模块导入失败: {e}")
        return False
    
    print("测试dataclass...")
    try:
        @dataclass
        class TestClass:
            name: str = "test"
            value: int = 0
        
        obj = TestClass()
        print(f"✓ dataclass测试成功: {obj}")
    except Exception as e:
        print(f"✗ dataclass测试失败: {e}")
        return False
    
    return True

def test_evennia_imports():
    """测试Evennia导入"""
    print("测试Evennia模块...")
    
    try:
        # 设置Django环境
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'server.conf.settings')
        
        import django
        django.setup()
        
        from evennia import logger
        print("✓ Evennia基础模块导入成功")
        
        return True
    except Exception as e:
        print(f"✗ Evennia模块导入失败: {e}")
        return False

def test_system_modules():
    """测试系统模块"""
    print("测试系统模块...")
    
    try:
        # 测试事件系统基础类
        from systems.event_system import EventPriority, EventStatus
        print("✓ 事件系统枚举导入成功")
        
        # 测试事件基类
        from systems.event_system import BaseEvent
        print("✓ 事件基类导入成功")
        
        # 创建事件实例
        event = BaseEvent(event_type="test")
        print(f"✓ 事件实例创建成功: {event.event_id}")
        
        return True
    except Exception as e:
        print(f"✗ 系统模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("简单系统测试开始")
    print("=" * 30)
    
    tests = [
        test_basic_imports,
        test_evennia_imports,
        test_system_modules
    ]
    
    for i, test in enumerate(tests, 1):
        print(f"\n{i}. {test.__doc__}")
        try:
            if not test():
                print("测试失败，停止后续测试")
                return False
        except Exception as e:
            print(f"测试异常: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    print("\n" + "=" * 30)
    print("🎉 所有测试通过！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
