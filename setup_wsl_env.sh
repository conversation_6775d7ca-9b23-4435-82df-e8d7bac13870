#!/bin/bash

# Bash脚本，用于在WSL中自动化仙侠MUD的完整环境搭建

# 步骤1：创建项目目录并进入
echo "--- 正在创建项目目录: ~/xiuxian_project ---"
mkdir -p ~/xiuxian_project
cd ~/xiuxian_project

# 步骤2：创建并激活Python虚拟环境
echo "--- 正在创建并激活Python虚拟环境 ---"
python3 -m venv venv
source venv/bin/activate

# 步骤3：安装所有必要的Python依赖
echo "--- 正在安装Evennia, psycopg2, 和 django-redis ---"
pip install evennia psycopg2-binary django-redis

# 步骤4：初始化Evennia项目
echo "--- 正在初始化Evennia游戏项目: xiuxian_mud ---"
evennia --init xiuxian_mud

# 步骤5：进入游戏项目目录
echo "--- 已进入游戏目录: xiuxian_mud ---"
cd xiuxian_mud

echo "--- 环境搭建初步完成！---"
echo "下一步: 配置 secret_settings.py 并执行数据库迁移。" 