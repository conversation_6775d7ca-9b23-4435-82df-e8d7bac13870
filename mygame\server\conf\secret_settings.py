"""
This file is meant for when you want to share your game dir with
others but don't want to share all details of your specific game
or local server setup. The settings in this file will override those
in settings.py and is in .gitignore by default.

A good guideline when sharing your game dir is that you want your
game to run correctly also without this file and only use this
to override your public, shared settings.

"""

# The secret key is randomly seeded upon creation. It is used to sign
# Django's cookies and should not be publicly known. It should also
# generally not be changed once people have registered with the game
# since it will invalidate their existing sessions.
SECRET_KEY = '^pPIj&<arG47T`#KWq@.h9-/k$R1EZiUFC)%wMx,'

# PostgreSQL Database Configuration
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'xiuxian_mud',
        'USER': 'postgres',
        'PASSWORD': 'zy123good',
        'HOST': 'localhost',
        'PORT': '5432',
        'ATOMIC_REQUESTS': True,
        'CONN_MAX_AGE': 300,
    }
}

# Redis Cache Configuration  
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# AI Configuration (will be expanded as we develop)
AI_SETTINGS = {
    'ENABLED': True,
    'DEFAULT_MODEL': 'gpt-3.5-turbo',
    'MAX_TOKENS': 1000,
    'TEMPERATURE': 0.7,
}

# AI API Keys (to be added later)
# OPENAI_API_KEY = ""
# ANTHROPIC_API_KEY = ""
