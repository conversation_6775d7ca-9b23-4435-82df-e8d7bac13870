#
# A batch-command file is a way to build a game world
# in a programmatic way, by placing a sequence of
# build commands after one another. This allows for
# using a real text editor to edit e.g. descriptions
# rather than entering text on the command line.
#
# A batch-command file is loaded with @batchprocess in-game:
#
#   @batchprocess[/interactive] tutorial_examples.batch_cmds
#
# A # as the first symbol on a line begins a comment and
# marks the end of a previous command definition. This is important,
# - every command must be separated by at least one line of comment.
#
# All supplied commands are given as normal, on their own line
# and accept arguments in any format up until the first next
# comment line begins. Extra whitespace is removed; an empty
# line in a command definition translates into a newline.
#
# See `evennia/contrib/tutorial_examples/batch_cmds.ev` for
# an example of a batch-command code. See also the batch-code
# system for loading python-code this way.
#


